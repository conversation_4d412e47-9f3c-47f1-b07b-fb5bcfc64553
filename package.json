{"name": "enterb2b", "version": "0.0.1", "author": "EnterERP Team", "private": true, "scripts": {"dev": "next dev -p 4444", "build": "yarn pre-build && NODE_OPTIONS='--max-old-space-size=4096' next build", "pre-build": "node ./pre-build.mjs", "start": "next start -p 4444", "analyze": "ANALYZE=true yarn build", "sync": "git reset --hard HEAD && git pull && yarn install && NEXT_IGNORE_CHECKS='true' yarn build && pm2 reload enterb2b"}, "dependencies": {"@headlessui/react": "1.7.15", "@stripe/react-stripe-js": "2.1.1", "@stripe/stripe-js": "1.54.0", "@tanstack/react-table": "^8.9.2", "@vechaiui/react": "0.2.2", "bcryptjs": "2.4.3", "cookies": "0.8.0", "fast-copy": "3.0.1", "i18next": "^23.2.0", "ioredis": "5.3.2", "js-base64": "3.7.5", "lodash.debounce": "4.0.8", "lodash.omit": "4.5.0", "lodash.orderby": "4.6.0", "lodash.template": "^4.5.0", "nanoid": "4.0.2", "next": "13.5.7", "next-auth": "4.22.1", "next-connect": "0.12.2", "next-i18next": "14.0.0", "next-pwa": "^5.6.0", "next-seo": "6.0.0", "react": "18.2.0", "react-cookie-consent": "8.0.1", "react-country-flag": "3.1.0", "react-currency-input-field": "^3.6.10", "react-datepicker": "^4.14.0", "react-dom": "18.2.0", "react-hook-form": "7.45.0", "react-i18next": "^13.0.0", "react-image-lightbox": "5.1.4", "react-merge-refs": "2.0.2", "react-number-format": "4.9.3", "react-rating": "2.0.5", "react-sticky-box": "1.0.2", "react-to-print": "^2.14.13", "request-ip": "^3.3.0", "sharp": "0.32.1", "sonner": "1.4.3", "swiper": "9.4.1", "yup": "1.2.0"}, "devDependencies": {"@next/bundle-analyzer": "13.4.6", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/forms": "0.5.3", "@tailwindcss/typography": "0.5.9", "@types/bcryptjs": "2.4.2", "@types/cookies": "0.7.7", "@types/facebook-pixel": "^0.0.25", "@types/gtag.js": "^0.0.12", "@types/lodash.debounce": "4.0.7", "@types/lodash.omit": "4.5.7", "@types/lodash.orderby": "4.6.7", "@types/lodash.template": "^4.5.1", "@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-datepicker": "^4.11.2", "@types/request-ip": "^0.0.38", "@vechaiui/core": "0.6.0", "autoprefixer": "10.4.14", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "postcss": "8.4.24", "postcss-import": "^15.1.0", "prettier": "2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "tailwindcss": "3.3.2", "typescript": "5.1.3"}, "prettier": {"tabWidth": 4, "semi": true, "bracketSpacing": false, "jsxBracketSameLine": false, "jsxSingleQuote": false, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "printWidth": 80, "plugins": ["prettier-plugin-tailwindcss"]}}