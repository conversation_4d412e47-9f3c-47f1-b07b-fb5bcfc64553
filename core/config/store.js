const theme = require('./theme');

module.exports = {
    paymentOnly: false,
    title: 'Enter-B2B',
    description: 'New generation B2B platform',
    titleTemplate: '%s - Enter-B2B',
    locales: ['tr'],
    defaultLocale: 'tr',
    defaultCurrency: {
        name: 'TL',
        symbol: '₺',
        template: '{{price}} {{name}}'
    },
    defaultCountry: {
        code: 'TR',
        name: '<PERSON>ürk<PERSON><PERSON>',
        phoneCode: '+90'
    },
    seo: {},
    theme,
    catalog: {
        productListItemFields: [
            'productId',
            'code',
            'name',
            'slug',
            'shortDescription',
            'rating',
            'reviewCount',
            'images',
            'salesPrice',
            'unDiscountedSalesPrice',
            'discount',
            'hasDiscount',
            'isBestSellingProduct',
            'isDiscountedProduct',
            'isNewProduct',
            'isSuggestedProduct',
            'estimatedDeliveryDuration'
        ],
        productsPerPage: 16,
        productImageShape: 'square'
    }
};
